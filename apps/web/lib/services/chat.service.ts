import { Message } from "database";
import {
  getAppointmentMessages,
  sendTextMessage,
  sendAudioMessage
} from "../../actions/chats/messages";
import { sendAttachmentWrapper } from "../../actions/appointments/messages/messages";
import { getRealtimeService, RealtimeConnectionStatus, RealtimeSubscription } from "./supabase-realtime.service";

export interface ChatMessage {
  id: string;
  content: string;
  type: 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM';
  senderId: string;
  appointmentId: string;
  createdAt: string;
  metadata?: Record<string, any>;
  senderRole?: string;
  // Campos para compatibilidade
  file_url?: string;
  file_name?: string;
  file_size?: number;
}

export interface ConnectionStatus {
  status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';
  lastConnected?: Date;
  reconnectAttempts: number;
  lastError?: string;
  isOnline: boolean;
  fallbackMode: boolean;
}

export class ChatService {
  private realtimeService = getRealtimeService();
  private messageSubscription: RealtimeSubscription | null = null;
  private statusSubscription: (() => void) | null = null;
  private connectionStatus: ConnectionStatus = {
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: true,
    fallbackMode: false
  };
  private messageHandlers: ((message: ChatMessage) => void)[] = [];
  private statusHandlers: ((status: ConnectionStatus) => void)[] = [];
  private isMounted = true;
  private retryCount = 0;
  private maxRetries = 3;

  constructor(private appointmentId: string) {
    // Configurar listener de status
    this.statusSubscription = this.realtimeService.onStatusChange((status) => {
      this.updateStatus(status.status, status.lastError, status.fallbackMode);
    });
  }

  async connect(): Promise<void> {
    if (this.messageSubscription) {
      this.disconnect();
    }

    console.log(`[ChatService] Conectando ao appointment: ${this.appointmentId}`);

    try {
      this.messageSubscription = await this.realtimeService.subscribeToMessages(
        this.appointmentId,
        (payload: any) => {
          console.log("[ChatService] Evento de mensagem recebido:", payload);

          if (!payload.new) {
            console.warn("[ChatService] Payload sem dados 'new'");
            return;
          }

          const newMessage = payload.new as Message;

          // Verificar se a mensagem é válida
          if (!newMessage || !newMessage.id) {
            console.error("[ChatService] Mensagem inválida recebida:", newMessage);
            return;
          }

          // Converter para o formato ChatMessage
          const chatMessage: ChatMessage = {
            id: newMessage.id,
            content: newMessage.content,
            type: newMessage.type as 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM',
            senderId: newMessage.senderId,
            appointmentId: newMessage.appointmentId,
            createdAt: newMessage.createdAt.toString(),
            metadata: newMessage.metadata as Record<string, any>,
            senderRole: newMessage.senderRole || undefined
          };

          console.log("[ChatService] Mensagem processada:", chatMessage.id);

          // Notificar handlers
          this.messageHandlers.forEach(handler => {
            try {
              handler(chatMessage);
            } catch (error) {
              console.error("[ChatService] Erro ao processar handler de mensagem:", error);
            }
          });
        }
      );

      // Configurar timeout para conexão
      setTimeout(() => {
        if (this.connectionStatus.status === 'connecting') {
          console.warn("[ChatService] Timeout de conexão, tentando novamente...");
          this.handleConnectionTimeout();
        }
      }, 15000); // 15 segundos

    } catch (error) {
      console.error("[ChatService] Erro ao conectar:", error);
      this.handleConnectionError(error as Error);
    }
  }

  private handleConnectionTimeout(): void {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      console.log(`[ChatService] Tentativa de reconexão ${this.retryCount}/${this.maxRetries}`);

      setTimeout(() => {
        if (this.isMounted) {
          this.connect();
        }
      }, 2000 * this.retryCount); // Backoff linear
    } else {
      console.error("[ChatService] Máximo de tentativas de reconexão atingido");
      this.updateStatus('error', 'Timeout de conexão após múltiplas tentativas');
    }
  }

  private handleConnectionError(error: Error): void {
    console.error("[ChatService] Erro de conexão:", error);

    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      console.log(`[ChatService] Tentativa de reconexão ${this.retryCount}/${this.maxRetries}`);

      setTimeout(() => {
        if (this.isMounted) {
          this.connect();
        }
      }, 3000 * this.retryCount); // Backoff exponencial
    } else {
      this.updateStatus('error', error.message);
    }
  }

  disconnect(): void {
    console.log(`[ChatService] Desconectando do appointment: ${this.appointmentId}`);

    if (this.messageSubscription) {
      try {
        this.messageSubscription.unsubscribe();
        this.messageSubscription = null;
      } catch (error) {
        console.error("[ChatService] Erro ao desconectar:", error);
      }
    }

    this.updateStatus('disconnected');
  }

  async getMessages(): Promise<ChatMessage[]> {
    try {
      console.log(`[ChatService] Buscando mensagens para appointment: ${this.appointmentId}`);

      const result = await getAppointmentMessages(this.appointmentId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao buscar mensagens');
      }

      if (!result.messages) {
        console.log("[ChatService] Nenhuma mensagem encontrada");
        return [];
      }

      // Converter para o formato ChatMessage
      const chatMessages: ChatMessage[] = result.messages.map((msg: Message) => ({
        id: msg.id,
        content: msg.content,
        type: msg.type as 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM',
        senderId: msg.senderId,
        appointmentId: msg.appointmentId,
        createdAt: msg.createdAt.toString(),
        metadata: msg.metadata as Record<string, any>,
        senderRole: msg.senderRole || undefined
      }));

      console.log(`[ChatService] ${chatMessages.length} mensagens carregadas`);
      return chatMessages;

    } catch (error) {
      console.error("[ChatService] Erro ao buscar mensagens:", error);
      throw error;
    }
  }

  async sendTextMessage(content: string, userId: string): Promise<ChatMessage> {
    try {
      console.log(`[ChatService] Enviando mensagem de texto: ${content.substring(0, 50)}...`);

      const result = await sendTextMessage(this.appointmentId, content, userId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao enviar mensagem');
      }

      if (!result.message) {
        throw new Error('Mensagem não retornada pelo servidor');
      }

      const message = result.message;

      // Converter para o formato ChatMessage
      const chatMessage: ChatMessage = {
        id: message.id,
        content: message.content,
        type: message.type as 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM',
        senderId: message.senderId,
        appointmentId: message.appointmentId,
        createdAt: message.createdAt.toString(),
        metadata: message.metadata as Record<string, any>,
        senderRole: message.senderRole || undefined
      };

      console.log("[ChatService] Mensagem enviada com sucesso:", chatMessage.id);
      return chatMessage;

    } catch (error) {
      console.error("[ChatService] Erro ao enviar mensagem de texto:", error);
      throw error;
    }
  }

  async sendAudioMessage(audioBlob: Blob, userId: string): Promise<ChatMessage> {
    try {
      console.log("[ChatService] Enviando mensagem de áudio...");

      const result = await sendAudioMessage(this.appointmentId, audioBlob, userId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao enviar áudio');
      }

      if (!result.message) {
        throw new Error('Mensagem de áudio não retornada pelo servidor');
      }

      const message = result.message;

      // Converter para o formato ChatMessage
      const chatMessage: ChatMessage = {
        id: message.id,
        content: message.content,
        type: message.type as 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM',
        senderId: message.senderId,
        appointmentId: message.appointmentId,
        createdAt: message.createdAt.toString(),
        metadata: message.metadata as Record<string, any>,
        senderRole: message.senderRole || undefined
      };

      console.log("[ChatService] Áudio enviado com sucesso:", chatMessage.id);
      return chatMessage;

    } catch (error) {
      console.error("[ChatService] Erro ao enviar áudio:", error);
      throw error;
    }
  }

  async sendFileMessage(file: File, userId: string): Promise<ChatMessage> {
    try {
      console.log(`[ChatService] Enviando arquivo: ${file.name}`);

      const result = await sendAttachmentWrapper(this.appointmentId, file, userId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao enviar arquivo');
      }

      if (!result.attachment) {
        throw new Error('Arquivo não retornado pelo servidor');
      }

      // Criar mensagem de sistema para o arquivo
      const chatMessage: ChatMessage = {
        id: `file-${Date.now()}`,
        content: `Arquivo enviado: ${file.name}`,
        type: 'FILE',
        senderId: userId,
        appointmentId: this.appointmentId,
        createdAt: new Date().toISOString(),
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          fileUrl: result.attachment.url
        },
        file_name: file.name,
        file_size: file.size,
        file_url: result.attachment.url
      };

      console.log("[ChatService] Arquivo enviado com sucesso:", chatMessage.id);
      return chatMessage;

    } catch (error) {
      console.error("[ChatService] Erro ao enviar arquivo:", error);
      throw error;
    }
  }

  onMessage(handler: (message: ChatMessage) => void): () => void {
    this.messageHandlers.push(handler);

    return () => {
      const index = this.messageHandlers.indexOf(handler);
      if (index > -1) {
        this.messageHandlers.splice(index, 1);
      }
    };
  }

  onStatusChange(handler: (status: ConnectionStatus) => void): () => void {
    this.statusHandlers.push(handler);

    // Enviar status atual imediatamente
    handler({ ...this.connectionStatus });

    return () => {
      const index = this.statusHandlers.indexOf(handler);
      if (index > -1) {
        this.statusHandlers.splice(index, 1);
      }
    };
  }

  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  reconnect(): void {
    console.log("[ChatService] Forçando reconexão...");
    this.retryCount = 0;
    this.realtimeService.reconnect();
  }

  destroy(): void {
    console.log("[ChatService] Destruindo serviço...");
    this.isMounted = false;

    if (this.statusSubscription) {
      this.statusSubscription();
      this.statusSubscription = null;
    }

    this.disconnect();
    this.messageHandlers = [];
    this.statusHandlers = [];
  }

  private updateStatus(status: ConnectionStatus['status'], error?: string, fallbackMode?: boolean): void {
    const previousStatus = this.connectionStatus.status;

    this.connectionStatus.status = status;
    if (error) {
      this.connectionStatus.lastError = error;
    }
    if (fallbackMode !== undefined) {
      this.connectionStatus.fallbackMode = fallbackMode;
    }

    if (status === 'connected') {
      this.connectionStatus.lastConnected = new Date();
      this.connectionStatus.lastError = undefined;
      this.retryCount = 0; // Reset retry count on successful connection
    }

    // Notificar apenas se o status mudou
    if (previousStatus !== status) {
      console.log(`[ChatService] Status atualizado: ${previousStatus} -> ${status}`);
      this.statusHandlers.forEach(handler => {
        try {
          handler({ ...this.connectionStatus });
        } catch (error) {
          console.error("[ChatService] Erro ao notificar handler de status:", error);
        }
      });
    }
  }
}
