import { Message } from "database";
import {
  getAppointmentMessages,
  sendTextMessage,
  sendAudioMessage
} from "../../actions/chats/messages";
import { sendAttachmentWrapper } from "../../actions/appointments/messages/messages";

export interface ChatMessage {
  id: string;
  content: string;
  type: 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM';
  senderId: string;
  appointmentId: string;
  createdAt: string;
  metadata?: Record<string, any>;
  senderRole?: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
}

export interface ConnectionStatus {
  status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';
  lastConnected?: Date;
  reconnectAttempts: number;
  lastError?: string;
  isOnline: boolean;
  mode: 'realtime' | 'polling' | 'hybrid';
}

export class ChatFallbackService {
  private appointmentId: string;
  private userId: string;
  private connectionStatus: ConnectionStatus = {
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: navigator.onLine,
    mode: 'polling'
  };
  private messageHandlers: ((message: ChatMessage) => void)[] = [];
  private statusHandlers: ((status: ConnectionStatus) => void)[] = [];
  private isMounted = true;
  private pollingInterval: NodeJS.Timeout | null = null;
  private lastMessageTimestamp: Date | null = null;
  private isPolling = false;
  private pollIntervalMs = 2000; // 2 segundos
  private maxPollIntervalMs = 10000; // 10 segundos máximo
  private consecutiveFailures = 0;
  private maxConsecutiveFailures = 3;

  constructor(appointmentId: string, userId: string) {
    this.appointmentId = appointmentId;
    this.userId = userId;
    this.setupOnlineStatusMonitoring();
  }

  private setupOnlineStatusMonitoring(): void {
    const updateOnlineStatus = () => {
      const wasOnline = this.connectionStatus.isOnline;
      this.connectionStatus.isOnline = navigator.onLine;

      if (wasOnline && !this.connectionStatus.isOnline) {
        console.log('[ChatFallback] Conexão offline detectada');
        this.updateStatus('disconnected');
        this.stopPolling();
      } else if (!wasOnline && this.connectionStatus.isOnline) {
        console.log('[ChatFallback] Conexão online restaurada');
        this.connect();
      }
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
  }

  async connect(): Promise<void> {
    if (!this.connectionStatus.isOnline) {
      console.log('[ChatFallback] Offline, não é possível conectar');
      return;
    }

    console.log(`[ChatFallback] Conectando ao appointment: ${this.appointmentId}`);
    this.updateStatus('connecting');

    try {
      // Primeiro, carregar mensagens existentes
      await this.loadExistingMessages();

      // Iniciar polling
      this.startPolling();

      this.updateStatus('connected');
      console.log('[ChatFallback] Conectado com sucesso via polling');

    } catch (error) {
      console.error('[ChatFallback] Erro ao conectar:', error);
      this.updateStatus('error', error instanceof Error ? error.message : 'Erro desconhecido');

      // Tentar reconectar após delay
      setTimeout(() => {
        if (this.isMounted) {
          this.connect();
        }
      }, 3000);
    }
  }

  private async loadExistingMessages(): Promise<void> {
    try {
      const messages = await this.getMessages();
      this.lastMessageTimestamp = messages.length > 0
        ? new Date(messages[messages.length - 1].createdAt)
        : new Date();

      console.log(`[ChatFallback] ${messages.length} mensagens existentes carregadas`);
    } catch (error) {
      console.error('[ChatFallback] Erro ao carregar mensagens existentes:', error);
      // Continuar mesmo com erro, usar timestamp atual
      this.lastMessageTimestamp = new Date();
    }
  }

  private startPolling(): void {
    if (this.isPolling) return;

    this.isPolling = true;
    console.log('[ChatFallback] Iniciando polling...');

    const poll = async () => {
      if (!this.isMounted || !this.isPolling) return;

      try {
        await this.checkForNewMessages();
        this.consecutiveFailures = 0;

        // Ajustar intervalo baseado no sucesso
        this.pollIntervalMs = Math.max(2000, this.pollIntervalMs * 0.9);

      } catch (error) {
        console.error('[ChatFallback] Erro durante polling:', error);
        this.consecutiveFailures++;

        // Aumentar intervalo em caso de falhas
        this.pollIntervalMs = Math.min(this.maxPollIntervalMs, this.pollIntervalMs * 1.5);

        if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
          console.warn('[ChatFallback] Muitas falhas consecutivas, pausando polling');
          this.updateStatus('error', 'Muitas falhas de conexão');
          return;
        }
      }

      // Agendar próximo poll
      this.pollingInterval = setTimeout(poll, this.pollIntervalMs);
    };

    // Iniciar primeiro poll
    poll();
  }

  private stopPolling(): void {
    this.isPolling = false;
    if (this.pollingInterval) {
      clearTimeout(this.pollingInterval);
      this.pollingInterval = null;
    }
    console.log('[ChatFallback] Polling parado');
  }

  private async checkForNewMessages(): Promise<void> {
    if (!this.lastMessageTimestamp) return;

    try {
      const messages = await this.getMessages();
      const newMessages = messages.filter(msg =>
        new Date(msg.createdAt) > this.lastMessageTimestamp!
      );

      if (newMessages.length > 0) {
        console.log(`[ChatFallback] ${newMessages.length} novas mensagens encontradas`);

        // Atualizar timestamp da última mensagem
        this.lastMessageTimestamp = new Date(newMessages[newMessages.length - 1].createdAt);

        // Notificar handlers sobre novas mensagens
        newMessages.forEach(message => {
          this.messageHandlers.forEach(handler => {
            try {
              handler(message);
            } catch (error) {
              console.error('[ChatFallback] Erro ao processar handler de mensagem:', error);
            }
          });
        });
      }
    } catch (error) {
      console.error('[ChatFallback] Erro ao verificar novas mensagens:', error);
      throw error;
    }
  }

  disconnect(): void {
    console.log(`[ChatFallback] Desconectando do appointment: ${this.appointmentId}`);
    this.stopPolling();
    this.updateStatus('disconnected');
  }

  async getMessages(): Promise<ChatMessage[]> {
    try {
      console.log(`[ChatFallback] Buscando mensagens para appointment: ${this.appointmentId}`);

      const result = await getAppointmentMessages(this.appointmentId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao buscar mensagens');
      }

      if (!result.messages) {
        console.log("[ChatFallback] Nenhuma mensagem encontrada");
        return [];
      }

      // Converter para o formato ChatMessage
      const chatMessages: ChatMessage[] = result.messages.map((msg: Message) => ({
        id: msg.id,
        content: msg.content,
        type: msg.type as 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM',
        senderId: msg.senderId,
        appointmentId: msg.appointmentId,
        createdAt: msg.createdAt.toString(),
        metadata: msg.metadata as Record<string, any>,
        senderRole: msg.senderRole || undefined
      }));

      console.log(`[ChatFallback] ${chatMessages.length} mensagens carregadas`);
      return chatMessages;

    } catch (error) {
      console.error("[ChatFallback] Erro ao buscar mensagens:", error);
      throw error;
    }
  }

  async sendTextMessage(content: string): Promise<ChatMessage> {
    try {
      console.log(`[ChatFallback] Enviando mensagem de texto: ${content.substring(0, 50)}...`);

      const result = await sendTextMessage(this.appointmentId, content, this.userId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao enviar mensagem');
      }

      if (!result.message) {
        throw new Error('Mensagem não retornada pelo servidor');
      }

      const message = result.message;

      // Converter para o formato ChatMessage
      const chatMessage: ChatMessage = {
        id: message.id,
        content: message.content,
        type: message.type as 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM',
        senderId: message.senderId,
        appointmentId: message.appointmentId,
        createdAt: message.createdAt.toString(),
        metadata: message.metadata as Record<string, any>,
        senderRole: message.senderRole || undefined
      };

      console.log("[ChatFallback] Mensagem enviada com sucesso:", chatMessage.id);

      // Atualizar timestamp para incluir a nova mensagem no próximo poll
      this.lastMessageTimestamp = new Date(chatMessage.createdAt);

      return chatMessage;

    } catch (error) {
      console.error("[ChatFallback] Erro ao enviar mensagem de texto:", error);
      throw error;
    }
  }

  async sendAudioMessage(audioBlob: Blob): Promise<ChatMessage> {
    try {
      console.log("[ChatFallback] Enviando mensagem de áudio...");

      const result = await sendAudioMessage(this.appointmentId, audioBlob, this.userId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao enviar áudio');
      }

      if (!result.message) {
        throw new Error('Mensagem de áudio não retornada pelo servidor');
      }

      const message = result.message;

      // Converter para o formato ChatMessage
      const chatMessage: ChatMessage = {
        id: message.id,
        content: message.content,
        type: message.type as 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM',
        senderId: message.senderId,
        appointmentId: message.appointmentId,
        createdAt: message.createdAt.toString(),
        metadata: message.metadata as Record<string, any>,
        senderRole: message.senderRole || undefined
      };

      console.log("[ChatFallback] Áudio enviado com sucesso:", chatMessage.id);

      // Atualizar timestamp
      this.lastMessageTimestamp = new Date(chatMessage.createdAt);

      return chatMessage;

    } catch (error) {
      console.error("[ChatFallback] Erro ao enviar áudio:", error);
      throw error;
    }
  }

  async sendFileMessage(file: File): Promise<ChatMessage> {
    try {
      console.log(`[ChatFallback] Enviando arquivo: ${file.name}`);

      const result = await sendAttachmentWrapper(this.appointmentId, file, this.userId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao enviar arquivo');
      }

      if (!result.attachment) {
        throw new Error('Arquivo não retornado pelo servidor');
      }

      // Criar mensagem de sistema para o arquivo
      const chatMessage: ChatMessage = {
        id: `file-${Date.now()}`,
        content: `Arquivo enviado: ${file.name}`,
        type: 'FILE',
        senderId: this.userId,
        appointmentId: this.appointmentId,
        createdAt: new Date().toISOString(),
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          fileUrl: result.attachment.url
        },
        file_name: file.name,
        file_size: file.size,
        file_url: result.attachment.url
      };

      console.log("[ChatFallback] Arquivo enviado com sucesso:", chatMessage.id);

      // Atualizar timestamp
      this.lastMessageTimestamp = new Date(chatMessage.createdAt);

      return chatMessage;

    } catch (error) {
      console.error("[ChatFallback] Erro ao enviar arquivo:", error);
      throw error;
    }
  }

  onMessage(handler: (message: ChatMessage) => void): () => void {
    this.messageHandlers.push(handler);

    return () => {
      const index = this.messageHandlers.indexOf(handler);
      if (index > -1) {
        this.messageHandlers.splice(index, 1);
      }
    };
  }

  onStatusChange(handler: (status: ConnectionStatus) => void): () => void {
    this.statusHandlers.push(handler);

    // Enviar status atual imediatamente
    handler({ ...this.connectionStatus });

    return () => {
      const index = this.statusHandlers.indexOf(handler);
      if (index > -1) {
        this.statusHandlers.splice(index, 1);
      }
    };
  }

  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  reconnect(): void {
    console.log("[ChatFallback] Forçando reconexão...");
    this.consecutiveFailures = 0;
    this.pollIntervalMs = 2000; // Reset para intervalo inicial
    this.connect();
  }

  destroy(): void {
    console.log("[ChatFallback] Destruindo serviço...");
    this.isMounted = false;
    this.stopPolling();
    this.messageHandlers = [];
    this.statusHandlers = [];
  }

  private updateStatus(status: ConnectionStatus['status'], error?: string): void {
    const previousStatus = this.connectionStatus.status;

    this.connectionStatus.status = status;
    if (error) {
      this.connectionStatus.lastError = error;
    }

    if (status === 'connected') {
      this.connectionStatus.lastConnected = new Date();
      this.connectionStatus.lastError = undefined;
      this.connectionStatus.mode = 'polling';
    }

    // Notificar apenas se o status mudou
    if (previousStatus !== status) {
      console.log(`[ChatFallback] Status atualizado: ${previousStatus} -> ${status}`);
      this.statusHandlers.forEach(handler => {
        try {
          handler({ ...this.connectionStatus });
        } catch (error) {
          console.error("[ChatFallback] Erro ao notificar handler de status:", error);
        }
      });
    }
  }
}
