import { useState, useEffect, useCallback, useRef } from 'react';
import { getRealtimeService, ChatMessage, ConnectionStatus, TypingStatus } from '../services/supabase-realtime.service';
import { getAppointmentMessages, sendTextMessage, sendAudioMessage } from '../../actions/chats/messages';
import { sendAttachmentWrapper } from '../../actions/appointments/messages/messages';

export interface UseSupabaseRealtimeChatOptions {
  appointmentId: string;
  userId: string;
  userName: string;
  autoConnect?: boolean;
  enableTypingIndicators?: boolean;
}

export interface UseSupabaseRealtimeChatReturn {
  // Estado das mensagens
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;

  // Estado da conexão
  connectionStatus: ConnectionStatus;
  isConnected: boolean;

  // Estado de digitação
  isTyping: boolean;
  typingUsers: TypingStatus[];

  // Funções de envio
  sendMessage: (content: string) => Promise<void>;
  sendAudio: (audioBlob: Blob) => Promise<void>;
  sendFile: (file: File) => Promise<void>;

  // Controle de conexão
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  reconnect: () => Promise<void>;
  refreshMessages: () => Promise<void>;

  // Controle de digitação
  startTyping: () => void;
  stopTyping: () => void;

  // Utilitários
  clearError: () => void;
}

/**
 * Hook principal para chat com Supabase Realtime
 * Substitui os hooks anteriores com uma implementação limpa e robusta
 */
export function useSupabaseRealtimeChat({
  appointmentId,
  userId,
  userName,
  autoConnect = true,
  enableTypingIndicators = true,
}: UseSupabaseRealtimeChatOptions): UseSupabaseRealtimeChatReturn {

  // Estados principais
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: true,
  });
  const [typingUsers, setTypingUsers] = useState<TypingStatus[]>([]);
  const [isTyping, setIsTyping] = useState(false);

  // Refs para controle
  const realtimeService = useRef(getRealtimeService());
  const isMountedRef = useRef(true);
  const processedMessageIds = useRef(new Set<string>());
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const unsubscribeRefs = useRef<(() => void)[]>([]);

  // Computed values
  const isConnected = connectionStatus.status === 'connected';

  // Função para verificar se mensagem já foi processada
  const isMessageProcessed = useCallback((messageId: string) => {
    return processedMessageIds.current.has(messageId);
  }, []);

  // Função para adicionar mensagem com verificação de duplicatas
  const addMessage = useCallback((message: ChatMessage) => {
    if (!message || !message.id || !isMountedRef.current) {
      return;
    }

    // Verificar se a mensagem já foi processada
    if (isMessageProcessed(message.id)) {
      console.log(`[useSupabaseRealtimeChat] Mensagem já processada: ${message.id}`);
      return;
    }

    // Marcar como processada
    processedMessageIds.current.add(message.id);

    console.log(`[useSupabaseRealtimeChat] Adicionando mensagem: ${message.id}`);

    setMessages(prevMessages => {
      // Verificação extra para garantir que não haja duplicatas
      if (prevMessages.some(m => m.id === message.id)) {
        console.log(`[useSupabaseRealtimeChat] Mensagem já existe no estado: ${message.id}`);
        return prevMessages;
      }

      // Ordenar por data de criação
      const updatedMessages = [...prevMessages, message].sort((a, b) => {
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      });

      return updatedMessages;
    });
  }, [isMessageProcessed]);

  // Carregar mensagens do servidor
  const loadMessages = useCallback(async () => {
    if (!appointmentId) {
      console.log('[useSupabaseRealtimeChat] Sem appointmentId para carregar mensagens');
      setMessages([]);
      setIsLoading(false);
      return;
    }

    console.log(`[useSupabaseRealtimeChat] Carregando mensagens para ${appointmentId}`);
    setIsLoading(true);
    setError(null);

    try {
      const result = await getAppointmentMessages(appointmentId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao carregar mensagens');
      }

      const serverMessages = result.messages || [];
      console.log(`[useSupabaseRealtimeChat] ${serverMessages.length} mensagens carregadas do servidor`);

      if (!isMountedRef.current) return;

      // Limpar cache de IDs processados e reprocessar
      processedMessageIds.current.clear();

      // Converter mensagens do servidor para o formato do chat
      const chatMessages: ChatMessage[] = serverMessages.map(msg => ({
        id: msg.id,
        content: msg.content,
        type: msg.type as 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM',
        senderId: msg.senderId,
        appointmentId: msg.appointmentId,
        createdAt: msg.createdAt.toString(),
        metadata: msg.metadata as Record<string, any>,
        senderRole: msg.senderRole || undefined,
      }));

      // Marcar todas as mensagens como processadas
      chatMessages.forEach(msg => {
        if (msg && msg.id) {
          processedMessageIds.current.add(msg.id);
        }
      });

      setMessages(chatMessages);
      setIsLoading(false);

    } catch (err) {
      console.error('[useSupabaseRealtimeChat] Erro ao carregar mensagens:', err);
      if (isMountedRef.current) {
        const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar mensagens';
        setError(errorMessage);
        setIsLoading(false);
      }
    }
  }, [appointmentId]);

  // Conectar ao chat
  const connect = useCallback(async () => {
    if (!appointmentId || !userId) {
      console.log('[useSupabaseRealtimeChat] Dados insuficientes para conectar');
      return;
    }

    console.log(`[useSupabaseRealtimeChat] Conectando ao chat ${appointmentId}`);
    setError(null);

    try {
      // Limpar subscriptions anteriores
      unsubscribeRefs.current.forEach(unsub => unsub());
      unsubscribeRefs.current = [];

      // Configurar handlers
      const unsubscribeMessage = realtimeService.current.onMessage(appointmentId, addMessage);
      const unsubscribeStatus = realtimeService.current.onStatusChange(appointmentId, setConnectionStatus);
      const unsubscribeTyping = realtimeService.current.onTypingChange(appointmentId, setTypingUsers);

      unsubscribeRefs.current.push(unsubscribeMessage, unsubscribeStatus, unsubscribeTyping);

      // Conectar ao serviço
      await realtimeService.current.connectToChat(appointmentId, userId);

    } catch (err) {
      console.error('[useSupabaseRealtimeChat] Erro ao conectar:', err);
      if (isMountedRef.current) {
        const errorMessage = err instanceof Error ? err.message : 'Erro ao conectar';
        setError(errorMessage);
      }
    }
  }, [appointmentId, userId, addMessage]);

  // Desconectar do chat
  const disconnect = useCallback(async () => {
    if (!appointmentId) return;

    console.log(`[useSupabaseRealtimeChat] Desconectando do chat ${appointmentId}`);

    // Limpar subscriptions
    unsubscribeRefs.current.forEach(unsub => unsub());
    unsubscribeRefs.current = [];

    // Desconectar do serviço
    await realtimeService.current.disconnectFromChat(appointmentId);
  }, [appointmentId]);

  // Reconectar
  const reconnect = useCallback(async () => {
    if (!appointmentId || !userId) return;

    console.log(`[useSupabaseRealtimeChat] Reconectando ao chat ${appointmentId}`);
    await disconnect();
    await connect();
  }, [appointmentId, userId, disconnect, connect]);

  // Atualizar mensagens
  const refreshMessages = useCallback(async () => {
    console.log('[useSupabaseRealtimeChat] Atualizando mensagens...');
    await loadMessages();
  }, [loadMessages]);

  // Enviar mensagem de texto
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || !appointmentId || !userId) {
      console.warn('[useSupabaseRealtimeChat] Dados insuficientes para enviar mensagem');
      return;
    }

    console.log(`[useSupabaseRealtimeChat] Enviando mensagem para ${appointmentId}`);
    setError(null);

    // Parar digitação
    stopTyping();

    // Criar mensagem otimística
    const optimisticMessage: ChatMessage = {
      id: `temp-${Date.now()}`,
      content: content.trim(),
      type: 'TEXT',
      senderId: userId,
      appointmentId,
      createdAt: new Date().toISOString(),
    };

    // Adicionar mensagem otimística
    addMessage(optimisticMessage);

    try {
      const result = await sendTextMessage(appointmentId, content.trim(), userId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao enviar mensagem');
      }

      console.log(`[useSupabaseRealtimeChat] Mensagem enviada com sucesso`);

      // Remover mensagem otimística
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);

    } catch (err) {
      console.error('[useSupabaseRealtimeChat] Erro ao enviar mensagem:', err);

      // Remover mensagem otimística
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);

      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar mensagem';
      setError(errorMessage);
      throw err;
    }
  }, [appointmentId, userId, addMessage]);

  // Enviar áudio
  const sendAudio = useCallback(async (audioBlob: Blob) => {
    if (!audioBlob || !appointmentId || !userId) {
      console.warn('[useSupabaseRealtimeChat] Dados insuficientes para enviar áudio');
      return;
    }

    console.log(`[useSupabaseRealtimeChat] Enviando áudio para ${appointmentId}`);
    setError(null);

    // Criar mensagem otimística
    const optimisticMessage: ChatMessage = {
      id: `temp-audio-${Date.now()}`,
      content: 'Enviando áudio...',
      type: 'AUDIO',
      senderId: userId,
      appointmentId,
      createdAt: new Date().toISOString(),
      file_size: audioBlob.size,
    };

    addMessage(optimisticMessage);

    try {
      const result = await sendAudioMessage(appointmentId, audioBlob, userId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao enviar áudio');
      }

      console.log(`[useSupabaseRealtimeChat] Áudio enviado com sucesso`);

      // Remover mensagem otimística
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);

    } catch (err) {
      console.error('[useSupabaseRealtimeChat] Erro ao enviar áudio:', err);

      // Remover mensagem otimística
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);

      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar áudio';
      setError(errorMessage);
      throw err;
    }
  }, [appointmentId, userId, addMessage]);

  // Enviar arquivo
  const sendFile = useCallback(async (file: File) => {
    if (!file || !appointmentId || !userId) {
      console.warn('[useSupabaseRealtimeChat] Dados insuficientes para enviar arquivo');
      return;
    }

    console.log(`[useSupabaseRealtimeChat] Enviando arquivo ${file.name} para ${appointmentId}`);
    setError(null);

    // Criar mensagem otimística
    const optimisticMessage: ChatMessage = {
      id: `temp-file-${Date.now()}`,
      content: file.name,
      type: 'FILE',
      senderId: userId,
      appointmentId,
      createdAt: new Date().toISOString(),
      file_name: file.name,
      file_size: file.size,
    };

    addMessage(optimisticMessage);

    try {
      const result = await sendAttachmentWrapper(appointmentId, file, userId);

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao enviar arquivo');
      }

      console.log(`[useSupabaseRealtimeChat] Arquivo enviado com sucesso`);

      // Remover mensagem otimística
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);

    } catch (err) {
      console.error('[useSupabaseRealtimeChat] Erro ao enviar arquivo:', err);

      // Remover mensagem otimística
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);

      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar arquivo';
      setError(errorMessage);
      throw err;
    }
  }, [appointmentId, userId, addMessage]);

  // Controle de digitação
  const startTyping = useCallback(() => {
    if (!enableTypingIndicators || !appointmentId || !userId || isTyping) return;

    console.log(`[useSupabaseRealtimeChat] Iniciando digitação para ${appointmentId}`);
    setIsTyping(true);

    // Enviar indicador de digitação
    realtimeService.current.sendTypingIndicator(appointmentId, userId, userName, true);

    // Limpar timeout anterior
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Configurar timeout para parar digitação automaticamente
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 3000);
  }, [enableTypingIndicators, appointmentId, userId, userName, isTyping]);

  const stopTyping = useCallback(() => {
    if (!enableTypingIndicators || !appointmentId || !userId || !isTyping) return;

    console.log(`[useSupabaseRealtimeChat] Parando digitação para ${appointmentId}`);
    setIsTyping(false);

    // Enviar indicador de parada de digitação
    realtimeService.current.sendTypingIndicator(appointmentId, userId, userName, false);

    // Limpar timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  }, [enableTypingIndicators, appointmentId, userId, userName, isTyping]);

  // Limpar erro
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Effect principal - inicializar quando appointmentId mudar
  useEffect(() => {
    if (!appointmentId || !userId) {
      console.log('[useSupabaseRealtimeChat] Effect: dados insuficientes, resetando estado');
      setMessages([]);
      setIsLoading(false);
      setError(null);
      setTypingUsers([]);
      return;
    }

    console.log(`[useSupabaseRealtimeChat] Effect: inicializando para ${appointmentId}`);

    // Reset state para novo appointment
    setMessages([]);
    setIsLoading(true);
    setError(null);
    setTypingUsers([]);
    processedMessageIds.current.clear();

    // Carregar mensagens e conectar
    const initializeChat = async () => {
      try {
        await loadMessages();
        if (autoConnect) {
          await connect();
        }
      } catch (err) {
        console.error('[useSupabaseRealtimeChat] Erro na inicialização:', err);
        // Em caso de erro, não tentar reconectar automaticamente
        // Apenas definir o estado de erro e deixar o usuário decidir
        if (isMountedRef.current) {
          const errorMessage = err instanceof Error ? err.message : 'Erro na inicialização';
          setError(errorMessage);
          setIsLoading(false);
        }
      }
    };

    initializeChat();

    // Cleanup
    return () => {
      console.log(`[useSupabaseRealtimeChat] Cleanup para ${appointmentId}`);

      // Parar digitação
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }

      // Limpar subscriptions
      unsubscribeRefs.current.forEach(unsub => unsub());
      unsubscribeRefs.current = [];
    };
  }, [appointmentId, userId, autoConnect, loadMessages]); // Removido connect das dependências para evitar loops

  // Effect para monitorar mudanças de status online/offline
  useEffect(() => {
    const handleOnlineStatusChange = () => {
      if (navigator.onLine && connectionStatus.status === 'disconnected') {
        console.log('[useSupabaseRealtimeChat] Conexão restaurada, tentando reconectar...');
        // Só reconectar se não estiver já conectando ou conectado
        if (connectionStatus.status !== 'connecting' && connectionStatus.status !== 'connected') {
          reconnect();
        }
      }
    };

    window.addEventListener('online', handleOnlineStatusChange);
    return () => window.removeEventListener('online', handleOnlineStatusChange);
  }, [connectionStatus.status]); // Removido reconnect das dependências para evitar loops

  // Effect de limpeza no unmount
  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;

      // Desconectar ao desmontar
      if (appointmentId) {
        realtimeService.current.disconnectFromChat(appointmentId);
      }

      // Limpar timeouts
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [appointmentId]);

  return {
    // Estado das mensagens
    messages,
    isLoading,
    error,

    // Estado da conexão
    connectionStatus,
    isConnected,

    // Estado de digitação
    isTyping,
    typingUsers,

    // Funções de envio
    sendMessage,
    sendAudio,
    sendFile,

    // Controle de conexão
    connect,
    disconnect,
    reconnect,
    refreshMessages,

    // Controle de digitação
    startTyping,
    stopTyping,

    // Utilitários
    clearError,
  };
}
