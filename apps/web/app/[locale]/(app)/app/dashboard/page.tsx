import { currentUser } from "@saas/auth/lib/current-user";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@ui/components/card";
import { StatusBlock } from "@ui/components/telemed/blocks/status-block";
import {
	Calendar,
	CreditCard,
	DollarSign,
	User,
	Users,
	Check,
	CheckCircle,
	X,
	XCircle,
	Clock,
	Percent,
	Activity as ActivityIcon,
	UserPlus,
	Stethoscope,
	CalendarClock
} from "lucide-react";

import { getDashboardMetrics, type DashboardMetrics } from "../../../../../actions/admin/get-dashboard-metrics";
import { AdminTabs } from "./client-tabs";
import { Overview, RecentAppointments, TopPerformers } from "./components/charts";
import { ReloadButton } from "./components/reload-button";
import { ErrorBoundary } from "./components/error-boundary";
import { redirect } from "next/navigation";
import RoleRedirect from "./components/role-redirect";
import { getLocale } from "next-intl/server";

// Utility function to format currency
const formatCurrency = (value: number): string => {
	return new Intl.NumberFormat('pt-BR', {
		style: 'currency',
		currency: 'BRL'
	}).format(value);
};

// Wrapper for client components that might fail
const SafeClientComponent = ({ children }: { children: React.ReactNode }) => (
	<ErrorBoundary>
		{children}
	</ErrorBoundary>
);

export default async function AdminPage() {
	// Handle authentication and routing
	try {
		const { user } = await currentUser();
		const locale = await getLocale();

		if (!user) {
			redirect(`/${locale}/auth/login`);
		}

		// Redirect based on role
		switch (user?.role) {
			case "DOCTOR":
				return <RoleRedirect role={user.role} locale={locale} onboardingComplete={user.onboardingComplete} />;
			case "USER":
			case "PATIENT":
				return <RoleRedirect role={user.role} locale={locale} onboardingComplete={user.onboardingComplete} />;
			case "HOSPITAL":
				return <RoleRedirect role={user.role} locale={locale} onboardingComplete={user.onboardingComplete} />;
            case "SECRETARY":
                return <RoleRedirect role={user.role} locale={locale} onboardingComplete={user.onboardingComplete} />;
			case "ADMIN":
				// Continue with admin dashboard
				break;
			default:
				return <RoleRedirect role={user?.role} locale={locale} onboardingComplete={user.onboardingComplete} />;
		}

		// Fetch dashboard metrics
		let dashboardMetrics: DashboardMetrics | null = null;
		try {
			dashboardMetrics = await getDashboardMetrics();
		} catch (metricsError) {
			console.error("Error fetching dashboard metrics:", metricsError);
			// Continue with null metrics, will show error state
		}

		// Prepare the content for each tab
		const overviewContent = dashboardMetrics && (
			<>
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<StatusBlock
						title="Total de Médicos"
						total={dashboardMetrics.doctors.total}
						icon={<Stethoscope className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Total de Pacientes"
						total={dashboardMetrics.patients.total}
						icon={<User className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Consultas"
						total={dashboardMetrics.appointments.total}
						icon={<CalendarClock className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Receita Total"
						total={formatCurrency(dashboardMetrics.transactions.revenue)}
						icon={<DollarSign className="h-8 w-8" />}
					/>
				</div>

				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
					<Card className="col-span-4">
						<CardHeader>
							<CardTitle>Análise de Consultas</CardTitle>
						</CardHeader>
						<CardContent className="pl-2">
							<SafeClientComponent>
								<Overview data={dashboardMetrics.appointments.byDay} />
							</SafeClientComponent>
						</CardContent>
					</Card>
					<Card className="col-span-3">
						<CardHeader>
							<CardTitle>Atividades Recentes</CardTitle>
							<CardDescription>
								Últimas atividades na plataforma
							</CardDescription>
						</CardHeader>
						<CardContent>
							<SafeClientComponent>
								<RecentAppointments data={dashboardMetrics.transactions.recent} />
							</SafeClientComponent>
						</CardContent>
					</Card>
				</div>
			</>
		);

		const doctorsContent = dashboardMetrics && (
			<>
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<StatusBlock
						title="Total de Médicos"
						total={dashboardMetrics.doctors.total}
						icon={<Stethoscope className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Pendentes de Aprovação"
						total={dashboardMetrics.doctors.pending}
						icon={<Clock className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Aprovados"
						total={dashboardMetrics.doctors.approved}
						icon={<CheckCircle className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Não Aprovados"
						total={dashboardMetrics.doctors.total - dashboardMetrics.doctors.approved - dashboardMetrics.doctors.pending}
						icon={<XCircle className="h-8 w-8" />}
					/>
				</div>

				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
					<Card className="col-span-4">
						<CardHeader>
							<CardTitle>Distribuição por Especialidade</CardTitle>
						</CardHeader>
						<CardContent className="pl-2">
							<SafeClientComponent>
								<Overview data={dashboardMetrics.doctors.bySpecialty} />
							</SafeClientComponent>
						</CardContent>
					</Card>
					<Card className="col-span-3">
						<CardHeader>
							<CardTitle>Médicos de Destaque</CardTitle>
						</CardHeader>
						<CardContent>
							<SafeClientComponent>
								<TopPerformers type="doctors" data={dashboardMetrics.topDoctors} />
							</SafeClientComponent>
						</CardContent>
					</Card>
				</div>
			</>
		);

		const patientsContent = dashboardMetrics && (
			<>
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
					<StatusBlock
						title="Total de Pacientes"
						total={dashboardMetrics.patients.total}
						icon={<User className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Novos Pacientes"
						total={dashboardMetrics.patients.newThisMonth}
						icon={<UserPlus className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Pacientes Ativos no Mês"
						total={dashboardMetrics.patients.newThisMonth} // Usando o mesmo valor por enquanto
						icon={<ActivityIcon className="h-8 w-8" />}
					/>
				</div>

				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
					<Card className="col-span-4">
						<CardHeader>
							<CardTitle>Crescimento de Pacientes</CardTitle>
						</CardHeader>
						<CardContent className="pl-2">
							<SafeClientComponent>
								<Overview data={dashboardMetrics.patients.newThisMonth ? [
									{ date: "Jan", count: Math.round(dashboardMetrics.patients.newThisMonth * 0.8) },
									{ date: "Fev", count: Math.round(dashboardMetrics.patients.newThisMonth * 0.9) },
									{ date: "Mar", count: dashboardMetrics.patients.newThisMonth },
								] : []} />
							</SafeClientComponent>
						</CardContent>
					</Card>
					<Card className="col-span-3">
						<CardHeader>
							<CardTitle>Pacientes Mais Frequentes</CardTitle>
						</CardHeader>
						<CardContent>
							<SafeClientComponent>
								<TopPerformers type="patients" data={[]} />
							</SafeClientComponent>
						</CardContent>
					</Card>
				</div>
			</>
		);

		const appointmentsContent = dashboardMetrics && (
			<>
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<StatusBlock
						title="Total de Consultas"
						total={dashboardMetrics.appointments.total}
						icon={<CalendarClock className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Consultas Agendadas"
						total={dashboardMetrics.appointments.scheduled}
						icon={<Calendar className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Consultas Realizadas"
						total={dashboardMetrics.appointments.completed}
						icon={<CheckCircle className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Consultas Canceladas"
						total={dashboardMetrics.appointments.canceled}
						icon={<X className="h-8 w-8" />}
					/>
				</div>

				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
					<Card className="col-span-4">
						<CardHeader>
							<CardTitle>Consultas por Tipo</CardTitle>
						</CardHeader>
						<CardContent className="pl-2">
							<SafeClientComponent>
								<Overview data={dashboardMetrics.appointments.byType} />
							</SafeClientComponent>
						</CardContent>
					</Card>
					<Card className="col-span-3">
						<CardHeader>
							<CardTitle>Próximas Consultas</CardTitle>
						</CardHeader>
						<CardContent>
							<SafeClientComponent>
								<RecentAppointments data={dashboardMetrics.transactions.recent} />
							</SafeClientComponent>
						</CardContent>
					</Card>
				</div>
			</>
		);

		const financeContent = dashboardMetrics && (
			<>
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<StatusBlock
						title="Receita Total"
						total={formatCurrency(dashboardMetrics.transactions.revenue)}
						icon={<DollarSign className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Taxas da Plataforma"
						total={formatCurrency(dashboardMetrics.transactions.platformFee)}
						icon={<Percent className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Pagamentos Pendentes"
						total={formatCurrency(dashboardMetrics.transactions.pendingAmount)}
						icon={<Clock className="h-8 w-8" />}
					/>
					<StatusBlock
						title="Transações Concluídas"
						total={dashboardMetrics.transactions.total}
						icon={<Check className="h-8 w-8" />}
					/>
				</div>

				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
					<Card className="col-span-4">
						<CardHeader>
							<CardTitle>Transações por Status</CardTitle>
						</CardHeader>
						<CardContent className="pl-2">
							<SafeClientComponent>
								<Overview data={dashboardMetrics.transactions.byStatus} />
							</SafeClientComponent>
						</CardContent>
					</Card>
					<Card className="col-span-3">
						<CardHeader>
							<CardTitle>Métodos de Pagamento</CardTitle>
						</CardHeader>
						<CardContent className="pl-2">
							<SafeClientComponent>
								<Overview data={dashboardMetrics.transactions.byPaymentMethod} />
							</SafeClientComponent>
						</CardContent>
					</Card>
				</div>
			</>
		);

		return (
			<div className="flex flex-col gap-6">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h1 className="text-2xl font-semibold tracking-tight">
							Dashboard Administrativo
						</h1>
						<p className="text-sm text-muted-foreground">
							Visão geral das métricas do sistema
						</p>
					</div>
				</div>

				{dashboardMetrics ? (
					<ErrorBoundary fallback={
						<Card>
							<CardHeader>
								<CardTitle className="text-red-500">Erro ao Renderizar Dashboard</CardTitle>
								<CardDescription>
									Ocorreu um erro ao renderizar os componentes do dashboard.
								</CardDescription>
							</CardHeader>
							<CardContent>
								<ReloadButton />
							</CardContent>
						</Card>
					}>
						<AdminTabs
							defaultTab="overview"
							overviewContent={overviewContent}
							doctorsContent={doctorsContent}
							patientsContent={patientsContent}
							appointmentsContent={appointmentsContent}
							financeContent={financeContent}
						/>
					</ErrorBoundary>
				) : (
					<Card>
						<CardHeader>
							<CardTitle className="text-red-500">Erro ao Carregar Dados</CardTitle>
							<CardDescription>
								Não foi possível carregar os dados do dashboard. Tente novamente em alguns instantes.
							</CardDescription>
						</CardHeader>
						<CardContent>
							<ReloadButton />
						</CardContent>
					</Card>
				)}
			</div>
		);
	} catch (error) {
		console.error("Dashboard error:", error);
		return (
			<div className="p-4">
				<h1 className="text-xl font-bold text-red-500">Erro ao Carregar Dashboard</h1>
				<p className="mb-4">Ocorreu um erro inesperado. Por favor, tente novamente.</p>
				<pre className="bg-gray-100 p-4 rounded overflow-auto">
					{error instanceof Error ? error.stack || error.message : String(error)}
				</pre>
				<ReloadButton />
			</div>
		);
	}
}
