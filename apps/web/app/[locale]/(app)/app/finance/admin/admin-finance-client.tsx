'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { DollarSign, Users, CreditCard, Percent, TrendingUp, Award } from "lucide-react";
import { FinancialMetricsCard } from "../components/financial-metrics-card";
import { RevenueChart } from "../components/revenue-chart";
import { PaymentMethodChart } from "../components/payment-method-chart";
import { TransactionsTable } from "../components/transactions-table";
import { FinancialFilters } from "../components/financial-filters";
import { Badge } from "@ui/components/badge";

interface AdminFinanceData {
  globalMetrics: {
    totalRevenue: number;
    platformFee: number;
    doctorRevenue: number;
    totalTransactions: number;
  };
  topDoctors: Array<{
    doctorId: string;
    doctorName: string;
    revenue: number;
    appointments: number;
  }>;
  chartData: Array<{
    date: string;
    revenue: number;
    platformFee: number;
    doctorRevenue: number;
  }>;
  statusDistribution: Array<{
    status: string;
    count: number;
    amount: number;
  }>;
  paymentMethods: Array<{
    method: string;
    count: number;
    amount: number;
  }>;
  transactions: Array<{
    id: string;
    amount: number;
    doctorAmount: number;
    platformFee: number;
    status: string;
    paymentMethod: string;
    paidAt?: string;
    createdAt: string;
    doctorName?: string;
    patientName?: string;
    appointmentDate?: string;
  }>;
}

export function AdminFinanceClient() {
  const [data, setData] = useState<AdminFinanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    period: '30days',
    status: 'all',
    method: 'all',
  });
  const [selectedDoctorId, setSelectedDoctorId] = useState('all');

  const fetchFinancialData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        ...filters,
        doctorId: selectedDoctorId,
      });
      const response = await fetch(`/api/finance/admin?${params}`);
      
      if (!response.ok) {
        throw new Error('Erro ao carregar dados financeiros');
      }
      
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      console.error('Error fetching financial data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFinancialData();
  }, [filters, selectedDoctorId]);

  const handleFiltersChange = (newFilters: typeof filters) => {
    setFilters(newFilters);
  };

  const handleDoctorChange = (doctorId: string) => {
    setSelectedDoctorId(doctorId);
  };

  // Preparar dados para exportação
  const prepareExportData = () => {
    if (!data?.transactions) return [];

    return data.transactions.map(t => ({
      'Data': new Date(t.createdAt).toLocaleDateString('pt-BR'),
      'Médico': t.doctorName || 'N/A',
      'Paciente': t.patientName || 'N/A',
      'Valor Total': `R$ ${t.amount.toFixed(2)}`,
      'Valor Médico': `R$ ${t.doctorAmount.toFixed(2)}`,
      'Taxa Plataforma': `R$ ${t.platformFee.toFixed(2)}`,
      'Status': t.status,
      'Método': t.paymentMethod,
      'Data Pagamento': t.paidAt ? new Date(t.paidAt).toLocaleDateString('pt-BR') : 'N/A',
      'Data Consulta': t.appointmentDate ? new Date(t.appointmentDate).toLocaleDateString('pt-BR') : 'N/A',
    }));
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4 md:p-6 space-y-6">
        <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div className="space-y-1">
            <h1 className="text-2xl md:text-3xl font-bold">Dashboard Financeiro - Admin</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Visão geral das finanças da plataforma
            </p>
          </div>
        </div>

        {/* Loading skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4 md:p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts loading */}
        <div className="grid gap-4 lg:grid-cols-7">
          <Card className="lg:col-span-4">
            <CardContent className="p-4 md:p-6">
              <div className="animate-pulse">
                <div className="h-6 bg-muted rounded w-1/3 mb-4"></div>
                <div className="h-[300px] bg-muted rounded"></div>
              </div>
            </CardContent>
          </Card>
          <Card className="lg:col-span-3">
            <CardContent className="p-4 md:p-6">
              <div className="animate-pulse">
                <div className="h-6 bg-muted rounded w-1/2 mb-4"></div>
                <div className="h-[300px] bg-muted rounded"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold">Dashboard Financeiro - Admin</h1>
            <p className="text-muted-foreground">
              Visão geral das finanças da plataforma
            </p>
          </div>
        </div>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p className="text-lg font-medium">Erro ao carregar dados</p>
              <p className="text-sm text-muted-foreground mt-2">{error}</p>
              <button 
                onClick={fetchFinancialData}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Tentar novamente
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="space-y-1">
          <h1 className="text-2xl md:text-3xl font-bold">Dashboard Financeiro - Admin</h1>
          <p className="text-sm md:text-base text-muted-foreground">
            Visão geral das finanças da plataforma
          </p>
        </div>
      </div>

      {/* Filtros */}
      <FinancialFilters
        onFiltersChange={handleFiltersChange}
        onRefresh={fetchFinancialData}
        loading={loading}
        showDoctorFilter={true}
        doctors={data?.topDoctors.map(d => ({ id: d.doctorId, name: d.doctorName })) || []}
        selectedDoctorId={selectedDoctorId}
        onDoctorChange={handleDoctorChange}
        exportData={prepareExportData()}
        exportFilename={`transacoes-admin-${new Date().toISOString().split('T')[0]}`}
      />

      {/* Métricas Globais */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <FinancialMetricsCard
          title="Receita Total da Plataforma"
          value={data?.globalMetrics.totalRevenue || 0}
          icon={DollarSign}
          trend="+15%"
          trendDirection="up"
          description="em relação ao mês anterior"
        />
        
        <FinancialMetricsCard
          title="Taxa da Plataforma"
          value={data?.globalMetrics.platformFee || 0}
          icon={Percent}
          trend="+12%"
          trendDirection="up"
          description="em relação ao mês anterior"
        />
        
        <FinancialMetricsCard
          title="Receita dos Médicos"
          value={data?.globalMetrics.doctorRevenue || 0}
          icon={Users}
          trend="+18%"
          trendDirection="up"
          description="em relação ao mês anterior"
        />
        
        <FinancialMetricsCard
          title="Total de Transações"
          value={data?.globalMetrics.totalTransactions || 0}
          icon={CreditCard}
          trend="+8%"
          trendDirection="up"
          description="em relação ao mês anterior"
        />
      </div>

      {/* Gráficos */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Evolução da Receita</CardTitle>
          </CardHeader>
          <CardContent>
            <RevenueChart 
              data={data?.chartData || []} 
              showPlatformFee={true}
              showDoctorRevenue={true}
            />
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Métodos de Pagamento</CardTitle>
          </CardHeader>
          <CardContent>
            <PaymentMethodChart 
              data={data?.paymentMethods || []} 
              showAmount={true}
            />
          </CardContent>
        </Card>
      </div>

      {/* Top Médicos e Status Distribution */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Top 10 Médicos por Receita</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data?.topDoctors.slice(0, 10).map((doctor, index) => (
                <div key={doctor.doctorId} className="flex items-center justify-between p-3 rounded-lg border">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                      <span className="text-sm font-medium text-primary">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">{doctor.doctorName}</p>
                      <p className="text-sm text-muted-foreground">
                        {doctor.appointments} consultas
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(doctor.revenue)}</p>
                    {index === 0 && (
                      <Badge variant="secondary" className="text-xs">
                        <Award className="w-3 h-3 mr-1" />
                        Top 1
                      </Badge>
                    )}
                  </div>
                </div>
              )) || (
                <div className="text-center text-muted-foreground py-8">
                  Nenhum dado disponível
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Distribuição por Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data?.statusDistribution.map((status) => (
                <div key={status.status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-primary"></div>
                    <span className="text-sm font-medium">{status.status}</span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{status.count} transações</p>
                    <p className="text-xs text-muted-foreground">
                      {formatCurrency(status.amount)}
                    </p>
                  </div>
                </div>
              )) || (
                <div className="text-center text-muted-foreground py-8">
                  Nenhum dado disponível
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabela de Transações */}
      <TransactionsTable
        transactions={data?.transactions || []}
        showPatientColumn={true}
        showDoctorColumn={true}
      />
    </div>
  );
}
