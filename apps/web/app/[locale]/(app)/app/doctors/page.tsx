// apps/web/app/[locale]/(saas)/app/doctors/page.tsx
import { currentUser } from "@saas/auth/lib/current-user";
import { Suspense } from "react";
import { redirect } from "next/navigation";
import { getLocale } from "next-intl/server";
import { DoctorsClient } from "./components/doctors-client";
import Loading from "./loading";
import { getDoctors } from "../../../../../actions/doctors/get-doctors";


export const dynamic = "force-dynamic";

export default async function DoctorsPage() {
	const { user } = await currentUser();
	const locale = await getLocale();

	// Verificar se o usuário está autenticado
	if (!user) {
		redirect(`/${locale}/auth/login`);
	}

	// Verificar se o usuário tem permissão para acessar esta página
	// Apenas ADMIN pode ver a lista de médicos
	if (user.role !== "ADMIN") {
		redirect(`/${locale}/app/dashboard`);
	}

	// Use the server action directly instead of API procedure
	const doctors = await getDoctors({});

	return (
		<Suspense fallback={<Loading />}>
			<DoctorsClient doctors={doctors} isAdmin={user?.role === "ADMIN"} />
		</Suspense>
	);
}
