// apps/web/app/[locale]/(saas)/app/doctors/components/doctor-card.tsx
'use client';

import {
	Calendar,
	CalendarDays,
	Check,
	Clock,
	Loader2,
	Mail,
	MoreVertical,
	Pencil,
	Phone,
	Star,
	User,
	X,
	ArrowRight,
	DollarSign,
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader } from '@ui/components/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@ui/components/dropdown-menu';
import { Separator } from '@ui/components/separator';
import {
	Sheet,
	Sheet<PERSON>ontent,
	SheetFooter,
	Sheet<PERSON>eader,
	SheetTitle,
} from '@ui/components/sheet';
import { SignedAvatar } from '../../../../../../components/shared/signed-avatar';
import { getCompleteImageUrl } from '../../../../../../lib/image-utils';

interface DoctorCardProps {
	doctor: {
		id: string;
		user: {
			name: string;
			email: string;
			image: string | null;
			phone: string | null;
		};
		crm: string;
		crmState: string;
		specialty: string;
		consultationPrice: number;
		consultationDuration: number;
		biography: string | null;
		documentStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
		rating: number | null;
		totalRatings: number;
		appointments: any[];
	};
	onEdit?: (id: string) => void;
	onDelete?: (id: string) => void;
	onApprove?: (id: string) => void;
	onReject?: (id: string) => void;
	onSchedule?: (id: string) => void;
}

export function DoctorCard({
	doctor,
	onEdit,
	onDelete,
	onApprove,
	onReject,
}: DoctorCardProps) {
	const router = useRouter();
	const [detailsOpen, setDetailsOpen] = useState(false);
	const [scheduleOpen, setScheduleOpen] = useState(false);
	const [isApproving, setIsApproving] = useState(false);
	const [isRejecting, setIsRejecting] = useState(false);

	console.log('Doctor card received doctor:', doctor);

	// Add null checks and better defaults for the user object
	const user = doctor.user || {
		id: '',
		name: 'Médico',
		email: '',
		emailVerified: false,
		phone: null,
		image: null,
	};

	// Get complete image URL for the doctor
	const imageUrl = getCompleteImageUrl(user.image);

	const statusConfig = {
		PENDING: { label: 'Pendente', color: 'bg-yellow-100 text-yellow-800' },
		APPROVED: { label: 'Aprovado', color: 'bg-green-100 text-green-800' },
		REJECTED: { label: 'Rejeitado', color: 'bg-red-100 text-red-800' },
	};

	// Função para lidar com a aprovação com loading
	const handleApprove = async () => {
		if (!onApprove) return;

		try {
			setIsApproving(true);
			await onApprove(doctor.id);
		} finally {
			setIsApproving(false);
		}
	};

	// Função para lidar com a rejeição com loading
	const handleReject = async () => {
		if (!onReject) return;

		try {
			setIsRejecting(true);
			await onReject(doctor.id);
		} finally {
			setIsRejecting(false);
		}
	};

	return (
		<>
			<Card
				className='group cursor-pointer transition-all duration-200 hover:shadow-xl hover:scale-[1.02] border-2 hover:border-blue-200 bg-gradient-to-br from-white to-gray-50'
				onClick={() => router.push(`/app/doctors/${doctor.id}`)}
			>
				<CardHeader className='pb-4'>
					<div className='flex items-start justify-between'>
						<div className='flex items-center gap-4'>
							<SignedAvatar
								imagePath={user.image}
								name={user.name || 'Médico'}
								className='h-16 w-16 ring-2 ring-white shadow-lg'
							/>
							<div className='flex-1 min-w-0'>
								<div className='flex items-center gap-2 mb-1'>
									<h3 className='font-bold text-lg text-gray-900 group-hover:text-blue-600 transition-colors'>
										{user.name || 'Médico'}
									</h3>
									<Badge
										className={`${statusConfig[doctor.documentStatus]?.color} text-xs px-2 py-1 shadow-sm`}
									>
										{statusConfig[doctor.documentStatus]?.label}
									</Badge>
								</div>
								<p className='text-blue-600 font-medium text-sm'>
									{doctor.specialty || 'Especialidade não definida'}
								</p>
								<p className='text-gray-500 text-sm'>
									CRM: {doctor.crm}/{doctor.crmState}
								</p>
							</div>
						</div>

						{(onEdit || onDelete || onApprove || onReject) && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
									<Button
										variant='ghost'
										size='icon'
										className='opacity-0 group-hover:opacity-100 transition-opacity'
									>
										<MoreVertical className='h-4 w-4' />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align='end'>
									{onEdit && (
										<DropdownMenuItem onClick={(e) => {
											e.stopPropagation();
											onEdit(doctor.id);
										}}>
											<Pencil className='mr-2 h-4 w-4' />
											Editar
										</DropdownMenuItem>
									)}

									{onApprove && doctor.documentStatus === 'PENDING' && (
										<DropdownMenuItem
											className='text-green-600'
											onClick={(e) => {
												e.stopPropagation();
												handleApprove();
											}}
											disabled={isApproving}
										>
											{isApproving ? (
												<Loader2 className='mr-2 h-4 w-4 animate-spin' />
											) : (
												<Check className='mr-2 h-4 w-4' />
											)}
											{isApproving ? 'Aprovando...' : 'Aprovar'}
										</DropdownMenuItem>
									)}
									{onReject && doctor.documentStatus === 'PENDING' && (
										<DropdownMenuItem
											className='text-destructive'
											onClick={(e) => {
												e.stopPropagation();
												handleReject();
											}}
											disabled={isRejecting}
										>
											{isRejecting ? (
												<Loader2 className='mr-2 h-4 w-4 animate-spin' />
											) : (
												<X className='mr-2 h-4 w-4' />
											)}
											{isRejecting ? 'Rejeitando...' : 'Rejeitar'}
										</DropdownMenuItem>
									)}
									{onDelete && (
										<DropdownMenuItem
											className='text-destructive'
											onClick={(e) => {
												e.stopPropagation();
												onDelete(doctor.id);
											}}
										>
											Excluir
										</DropdownMenuItem>
									)}
								</DropdownMenuContent>
							</DropdownMenu>
						)}
					</div>
				</CardHeader>

				<CardContent className='pt-0'>
					<div className='grid grid-cols-2 gap-4 mb-4'>
						<div className='flex items-center gap-2 text-sm'>
							<div className='p-2 bg-green-100 rounded-lg'>
								<DollarSign className='h-4 w-4 text-green-600' />
							</div>
							<div>
								<p className='font-medium text-gray-900'>
									R$ {doctor.consultationPrice ? Number(doctor.consultationPrice).toFixed(2) : '0,00'}
								</p>
								<p className='text-gray-500 text-xs'>Valor/Consulta</p>
							</div>
						</div>

						<div className='flex items-center gap-2 text-sm'>
							<div className='p-2 bg-blue-100 rounded-lg'>
								<Clock className='h-4 w-4 text-blue-600' />
							</div>
							<div>
								<p className='font-medium text-gray-900'>{doctor.consultationDuration}min</p>
								<p className='text-gray-500 text-xs'>Duração</p>
							</div>
						</div>
					</div>

					{doctor.rating && (
						<div className='flex items-center gap-2 mb-4'>
							<div className='flex items-center gap-1'>
								<Star className='h-4 w-4 text-yellow-500 fill-current' />
								<span className='font-medium text-gray-900'>
									{doctor.rating.toFixed(1)}
								</span>
							</div>
							<span className='text-gray-500 text-sm'>
								({doctor.totalRatings} avaliações)
							</span>
						</div>
					)}

					<div className='flex items-center justify-between pt-4 border-t border-gray-100'>
						<div className='text-sm text-gray-500 group-hover:text-blue-600 transition-colors'>
							Ver detalhes completos
						</div>
						<div className='text-blue-600 group-hover:text-blue-700 transition-colors group-hover:translate-x-1 transform duration-200'>
							<ArrowRight className='w-4 h-4' />
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Details Sheet */}
			<Sheet open={detailsOpen} onOpenChange={setDetailsOpen}>
				<SheetContent className='w-full overflow-y-auto min:max-w-3xl'>
					<SheetHeader>
						<SheetTitle>Detalhes do Médico</SheetTitle>
					</SheetHeader>
					<div className='mt-6 space-y-6'>
						<div className='flex items-start gap-4'>
							<SignedAvatar
								imagePath={user.image}
								name={user.name || 'Médico'}
								className='h-20 w-20'
							/>
							<div className='space-y-1'>
								<h3 className='font-semibold'>{doctor.user.name}</h3>
								<div className='text-sm text-muted-foreground'>
									{doctor.specialty}
								</div>
							</div>
						</div>

						<Separator />

						<div className='space-y-4'>
							<div className='space-y-2'>
								<h4 className='font-medium'>Informações de Contato</h4>
								<div className='space-y-2 text-sm'>
									<div className='flex items-center gap-2'>
										<Mail className='h-4 w-4 text-gray-500' />
										{doctor.user.email}
									</div>
									{doctor.user.phone && (
										<div className='flex items-center gap-2'>
											<Phone className='h-4 w-4 text-gray-500' />
											{doctor.user.phone}
										</div>
									)}
								</div>
							</div>

							<div className='space-y-2'>
								<h4 className='font-medium'>Informações Profissionais</h4>
								<div className='space-y-2 text-sm'>
									<div className='flex items-center gap-2'>
										<User className='h-4 w-4 text-gray-500' />
										CRM: {doctor.crm}/{doctor.crmState}
									</div>
									<div className='flex items-center gap-2'>
										<CalendarDays className='h-4 w-4 text-gray-500' />
										Consultas realizadas: {doctor.appointments.length}
									</div>
								</div>
							</div>

							{doctor.biography && (
								<div className='space-y-2'>
									<h4 className='font-medium'>Biografia</h4>
									<p className='text-sm'>{doctor.biography}</p>
								</div>
							)}
						</div>

						<SheetFooter>
							{onEdit && (
								<Button
									onClick={() => {
										onEdit(doctor.id);
										setDetailsOpen(false);
									}}
								>
									Editar Médico
								</Button>
							)}
						</SheetFooter>
					</div>
				</SheetContent>
			</Sheet>
		</>
	);
}
