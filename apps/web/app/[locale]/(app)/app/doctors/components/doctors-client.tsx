// apps/web/app/[locale]/(saas)/app/doctors/components/doctors-client.tsx
'use client';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { apiClient } from '@shared/lib/api-client';
import { DashboardHeader } from '@ui/components/header';
import { Input } from '@ui/components/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import { Button } from '@ui/components/button';
import { Plus, User } from 'lucide-react';
import { DeleteConfirmation } from '../../specialties/components/delete-confirmation';
import { DoctorCard } from './doctor-card';
import { DoctorForm } from './doctor-form';

import { AddDoctorForm } from './add-doctor-form';

interface DoctorsClientProps {
	doctors: any[];
	isAdmin: boolean;
}

export function DoctorsClient({
	doctors: initialDoctors,
	isAdmin,
}: DoctorsClientProps) {
	const router = useRouter();
	const [doctors, setDoctors] = useState(initialDoctors);
	const [formOpen, setFormOpen] = useState(false);
	const [addDoctorOpen, setAddDoctorOpen] = useState(false);
	const [scheduleOpen, setScheduleOpen] = useState(false);
	const [deleteOpen, setDeleteOpen] = useState(false);
	const [selectedDoctor, setSelectedDoctor] = useState<any>(null);
	const [doctorToDelete, setDoctorToDelete] = useState<string | null>(null);
	const [searchTerm, setSearchTerm] = useState('');
	const [statusFilter, setStatusFilter] = useState<
		'ALL' | 'PENDING' | 'APPROVED' | 'REJECTED'
	>('ALL');

	// Mutations
	const deleteDoctorMutation = apiClient.doctors.delete_.useMutation();
	const approveDoctorMutation = apiClient.doctors.approve.useMutation();
	const rejectDoctorMutation = apiClient.doctors.reject.useMutation();

	// Filter doctors by search term and status
	const filteredDoctors = doctors.filter((doctor) => {
		const matchesSearch =
			doctor.user.name?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
			doctor.user.email?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
			doctor.specialty?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
			doctor.crm?.includes(searchTerm);

		const matchesStatus =
			statusFilter === 'ALL' || doctor.status === statusFilter;

		return matchesSearch && matchesStatus;
	});

	const handleApprove = async (id: string) => {
		try {
			await approveDoctorMutation.mutateAsync({ id });
			setDoctors(
				doctors.map((d) =>
					d.id === id ? { ...d, documentStatus: 'APPROVED' } : d
				)
			);
			toast.success('Médico aprovado com sucesso');

			// Forçar atualização da lista
			setTimeout(() => {
				router.refresh();
			}, 500);
		} catch (error) {
			console.error('Error approving doctor:', error);
			toast.error(
				error instanceof Error ? error.message : 'Erro ao aprovar médico'
			);
		}
	};

	const handleReject = async (id: string) => {
		try {
			await rejectDoctorMutation.mutateAsync({ id });
			setDoctors(
				doctors.map((d) =>
					d.id === id ? { ...d, documentStatus: 'REJECTED' } : d
				)
			);
			toast.success('Médico rejeitado com sucesso');

			// Forçar atualização da lista
			setTimeout(() => {
				router.refresh();
			}, 500);
		} catch (error) {
			console.error('Error rejecting doctor:', error);
			toast.error(
				error instanceof Error ? error.message : 'Erro ao rejeitar médico'
			);
		}
	};

	const handleEdit = (id: string) => {
		const doctor = doctors.find((d) => d.id === id);
		if (doctor) {
			setSelectedDoctor(doctor);
			setFormOpen(true);
		}
	};

	const handleSchedule = (id: string) => {
		setSelectedDoctor(doctors.find((d) => d.id === id));
		setScheduleOpen(true);
	};

	const handleDelete = (id: string) => {
		setDoctorToDelete(id);
		setDeleteOpen(true);
	};

	const handleDeleteConfirm = async () => {
		if (!doctorToDelete) return;

		try {
			await deleteDoctorMutation.mutateAsync({
				id: doctorToDelete,
			});

			setDoctors(doctors.filter((d) => d.id !== doctorToDelete));
			toast.success('Médico excluído com sucesso!');
			setDeleteOpen(false);
			setDoctorToDelete(null);
			router.refresh();
		} catch (error) {
			toast.error(
				error instanceof Error ? error.message : 'Erro ao excluir médico'
			);
		}
	};

	const handleAddDoctor = () => {
		setAddDoctorOpen(true);
	};

	return (
		<div className='flex flex-col gap-6'>
			<div className='flex items-center justify-between'>
				<DashboardHeader
					heading='Médicos'
					text='Gerencie os médicos cadastrados no sistema.'
				/>

				<div className='flex flex-1 items-center justify-end gap-4'>
					<Input
						className='max-w-md'
						placeholder='Buscar por nome, email, especialidade ou CRM...'
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
					/>

					<Select
						value={statusFilter}
						onValueChange={(value: any) => setStatusFilter(value)}
					>
						<SelectTrigger className='w-[180px]'>
							<SelectValue placeholder='Status' />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value='ALL'>Todos</SelectItem>
							<SelectItem value='PENDING'>Pendentes</SelectItem>
							<SelectItem value='APPROVED'>Aprovados</SelectItem>
							<SelectItem value='REJECTED'>Rejeitados</SelectItem>
						</SelectContent>
					</Select>

					{isAdmin && (
						<Button onClick={handleAddDoctor} variant='default'>
							<Plus className='mr-2 h-4 w-4' />
							Criar Médico
						</Button>
					)}
				</div>
			</div>

			<div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
				{filteredDoctors.length === 0 ? (
					<div className='col-span-full py-16 text-center'>
						<div className='mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4'>
							<User className='w-12 h-12 text-gray-400' />
						</div>
						<h3 className='text-lg font-medium text-gray-900 mb-2'>Nenhum médico encontrado</h3>
						<p className='text-gray-500 mb-4'>
							{searchTerm || statusFilter !== 'ALL'
								? 'Tente ajustar os filtros de busca.'
								: 'Comece adicionando um novo médico ao sistema.'
							}
						</p>
						{isAdmin && !searchTerm && statusFilter === 'ALL' && (
							<Button onClick={handleAddDoctor} variant='outline'>
								<Plus className='mr-2 h-4 w-4' />
								Adicionar Primeiro Médico
							</Button>
						)}
					</div>
				) : (
					filteredDoctors.map((doctor) => (
						<DoctorCard
							key={doctor.id}
							doctor={doctor}
							onEdit={handleEdit}
							onDelete={handleDelete}
							onApprove={isAdmin ? handleApprove : undefined}
							onReject={isAdmin ? handleReject : undefined}
							onSchedule={handleSchedule}
						/>
					))
				)}
			</div>

			<DoctorForm
				doctor={selectedDoctor}
				open={formOpen}
				onOpenChange={(open) => {
					setFormOpen(open);
					if (!open) setSelectedDoctor(null);
				}}
			/>

			{isAdmin && (
				<AddDoctorForm
					open={addDoctorOpen}
					onOpenChange={(open) => {
						setAddDoctorOpen(open);
						if (!open) router.refresh();
					}}
				/>
			)}

			<DeleteConfirmation
				open={deleteOpen}
				onOpenChange={setDeleteOpen}
				onConfirm={handleDeleteConfirm}
				isLoading={deleteDoctorMutation.isPending}
			/>
		</div>
	);
}
